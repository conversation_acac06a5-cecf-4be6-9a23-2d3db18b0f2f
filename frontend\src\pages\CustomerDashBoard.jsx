import {
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Filter,
  Paperclip,
  Plus,
  RotateCcw,
  Search,
  Trash2,
  XCircle,
} from "lucide-react";
import { useState } from "react";

const CustomerDashboard = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Sample ticket data
  const tickets = [
    {
      id: "TK-001",
      subject: "Login Issues",
      category: "Technical",
      status: "In Progress",
      priority: "High",
      created: "2025-05-25",
      lastUpdate: "2 hours ago",
      agent: "<PERSON>",
      canDelete: false,
    },
    {
      id: "TK-002",
      subject: "Feature Request - Dark Mode",
      category: "Feature Request",
      status: "Pending Approval",
      priority: "Medium",
      created: "2025-05-26",
      lastUpdate: "5 minutes ago",
      agent: null,
      canDelete: true,
    },
    {
      id: "TK-003",
      subject: "Billing Question",
      category: "Billing",
      status: "Resolved",
      priority: "Low",
      created: "2025-05-20",
      lastUpdate: "3 days ago",
      agent: "Sarah Johnson",
      canDelete: false,
    },
    {
      id: "TK-004",
      subject: "API Documentation",
      category: "Documentation",
      status: "Rejected",
      priority: "Medium",
      created: "2025-05-24",
      lastUpdate: "1 day ago",
      agent: null,
      canDelete: false,
    },
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "In Progress":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "bg-red-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && ticket.status === "Pending Approval") ||
      (activeTab === "active" &&
        ["In Progress", "Seen"].includes(ticket.status)) ||
      (activeTab === "resolved" && ticket.status === "Resolved") ||
      (activeTab === "rejected" && ticket.status === "Rejected");
    return matchesSearch && matchesTab;
  });

  const CreateTicketModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
        <h3 className="text-lg font-semibold mb-4">Create New Ticket</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Subject *</label>
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Brief description of your issue"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Category *</label>
            <select className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>Technical</option>
              <option>Billing</option>
              <option>Feature Request</option>
              <option>Documentation</option>
              <option>Other</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              Description *
            </label>
            <textarea
              rows="4"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Detailed description of your issue or request"
            ></textarea>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              Attachments
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
              <Paperclip className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">
                Drop files here or click to upload
              </p>
              <p className="text-xs text-gray-500">Max 5 files, 25MB each</p>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowCreateModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Create Ticket
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                My Support Tickets
              </h1>
              <p className="text-sm text-gray-600">
                Track and manage your support requests
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-3 h-3 mr-1" />
              New Ticket
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-3">
          <div className="bg-white rounded-lg p-3 shadow-sm">
            <div className="flex items-center">
              <div className="p-1.5 bg-blue-100 rounded-lg">
                <MessageSquare className="w-4 h-4 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">
                  Total Tickets
                </p>
                <p className="text-lg font-bold text-gray-900">4</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-3 shadow-sm">
            <div className="flex items-center">
              <div className="p-1.5 bg-yellow-100 rounded-lg">
                <Clock className="w-4 h-4 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">
                  Pending Approval
                </p>
                <p className="text-lg font-bold text-gray-900">1</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-3 shadow-sm">
            <div className="flex items-center">
              <div className="p-1.5 bg-blue-100 rounded-lg">
                <AlertCircle className="w-4 h-4 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">In Progress</p>
                <p className="text-lg font-bold text-gray-900">1</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-3 shadow-sm">
            <div className="flex items-center">
              <div className="p-1.5 bg-green-100 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">Resolved</p>
                <p className="text-lg font-bold text-gray-900">1</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm mb-3">
          <div className="p-3">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
              <div className="flex space-x-1">
                {[
                  { key: "all", label: "All Tickets", count: 4 },
                  { key: "pending", label: "Pending Approval", count: 1 },
                  { key: "active", label: "Active", count: 1 },
                  { key: "resolved", label: "Resolved", count: 1 },
                  { key: "rejected", label: "Rejected", count: 1 },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key)}
                    className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                      activeTab === tab.key
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }`}
                  >
                    {tab.label} ({tab.count})
                  </button>
                ))}
              </div>
              <div className="flex space-x-2">
                <div className="relative">
                  <Search className="w-3 h-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tickets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button className="flex items-center px-2 py-1.5 text-xs border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  <Filter className="w-3 h-3 mr-1" />
                  Filter
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tickets List */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ticket
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Agent
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Update
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTickets.map((ticket) => (
                  <tr key={ticket.id} className="hover:bg-gray-50">
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {ticket.id}
                        </div>
                        <div className="text-xs text-gray-500">
                          {ticket.subject}
                        </div>
                        <div className="text-xs text-gray-400">
                          {ticket.category}
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(ticket.status)}
                        <span
                          className={`ml-1 px-1.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(
                            ticket.status
                          )}`}
                        >
                          {ticket.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div
                          className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
                            ticket.priority
                          )}`}
                        ></div>
                        <span className="text-xs text-gray-900">
                          {ticket.priority}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-900">
                      {ticket.agent || "Unassigned"}
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
                      {ticket.lastUpdate}
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
                      <div className="flex space-x-1">
                        <button className="text-blue-600 hover:text-blue-900">
                          <Eye className="w-3 h-3" />
                        </button>
                        {ticket.status === "Resolved" && (
                          <button className="text-green-600 hover:text-green-900">
                            <RotateCcw className="w-3 h-3" />
                          </button>
                        )}
                        {ticket.canDelete && (
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Create Ticket Modal */}
      {showCreateModal && <CreateTicketModal />}
    </div>
  );
};

export default CustomerDashboard;
