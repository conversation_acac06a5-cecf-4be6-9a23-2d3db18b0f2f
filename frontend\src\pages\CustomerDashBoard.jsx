import { Plus } from "lucide-react";
import { useState } from "react";
import CreateTicketModal from "../components/CreateTicketModal";
import TicketDetailModal from "../components/TicketDetailModal";
import TicketFilters from "../components/TicketFilters";
import TicketTable from "../components/TicketTable";

const CustomerDashboard = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Sample ticket data with enhanced information
  const tickets = [
    {
      id: "TK-001",
      subject: "Login Issues",
      category: "Technical",
      status: "In Progress",
      priority: "High",
      created: "2025-05-25",
      lastUpdate: "2 hours ago",
      agent: "<PERSON>",
      canDelete: false,
      description:
        "I'm experiencing login issues with my account. When I try to log in, I get an error message saying 'Invalid credentials' even though I'm using the correct username and password. This started happening after the recent system update.",
    },
    {
      id: "TK-002",
      subject: "Feature Request - Dark Mode",
      category: "Feature Request",
      status: "Pending Approval",
      priority: "Medium",
      created: "2025-05-26",
      lastUpdate: "5 minutes ago",
      agent: null,
      canDelete: true,
      description:
        "It would be great to have a dark mode option in the application. Many users prefer dark themes, especially when working in low-light environments. This would improve user experience and reduce eye strain.",
    },
    {
      id: "TK-003",
      subject: "Billing Question",
      category: "Billing",
      status: "Resolved",
      priority: "Low",
      created: "2025-05-20",
      lastUpdate: "3 days ago",
      agent: "Sarah Johnson",
      canDelete: false,
      description:
        "I have a question about my recent billing statement. There seems to be a charge that I don't recognize. Could you please help me understand what this charge is for?",
    },
    {
      id: "TK-004",
      subject: "API Documentation",
      category: "Documentation",
      status: "Rejected",
      priority: "Medium",
      created: "2025-05-24",
      lastUpdate: "1 day ago",
      agent: null,
      canDelete: false,
      description:
        "The API documentation seems to be missing some important endpoints. Specifically, I can't find documentation for the user management endpoints that were mentioned in the changelog.",
    },
  ];

  // Handler functions
  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowDetailModal(true);
  };

  const handleTicketAction = (action, ticket) => {
    switch (action) {
      case "reopen":
        console.log("Reopening ticket:", ticket.id);
        // Implement reopen logic
        break;
      case "delete":
        if (window.confirm("Are you sure you want to delete this ticket?")) {
          console.log("Deleting ticket:", ticket.id);
          // Implement delete logic
        }
        break;
      default:
        break;
    }
  };

  const handleCreateTicket = async (ticketData) => {
    console.log("Creating new ticket:", ticketData);
    // Implement ticket creation logic
    // This would typically involve an API call
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && ticket.status === "Pending Approval") ||
      (activeTab === "active" &&
        ["In Progress", "Seen"].includes(ticket.status)) ||
      (activeTab === "resolved" && ticket.status === "Resolved") ||
      (activeTab === "rejected" && ticket.status === "Rejected");
    return matchesSearch && matchesTab;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                My Support Tickets
              </h1>
              <p className="text-sm text-gray-600">
                Track and manage your support requests
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-3 h-3 mr-1" />
              New Ticket
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        <TicketFilters
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          tickets={tickets}
        />

        {/* Tickets List */}
        <TicketTable
          tickets={filteredTickets}
          onTicketClick={handleTicketClick}
          onTicketAction={handleTicketAction}
        />
      </div>

      {/* Modals */}
      <CreateTicketModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateTicket}
      />

      <TicketDetailModal
        ticket={selectedTicket}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
      />
    </div>
  );
};

export default CustomerDashboard;
